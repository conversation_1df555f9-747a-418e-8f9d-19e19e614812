"use client";
import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "@heroui/link";
import { insightsData } from "@/types/insightData";

interface InsightsScreenProps {}

interface InsightCard {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  date: string;
  image: string;
}

const InsightsScreen: React.FC<InsightsScreenProps> = () => {
  const { t, isRTL } = useLanguage();

  // Map master data into card format
  const insights: InsightCard[] = insightsData.map((article, index) => ({
    id: index + 1,
    title: article.title,
    excerpt: article.hook,
    category: t(`insights.categories.${getCategoryKey(article.title)}`),
    readTime: `${Math.ceil((article.hook.split(" ").length / 200) * 5)} ${t(
      "common.minRead"
    )}`,
    date: getDateForIndex(index),
    image: article.image,
  }));

  return (
    <div
      className={`bg-gradient-to-br from-slate-50 to-white min-h-screen ${
        isRTL ? "rtl" : "ltr"
      }`}
    >
      {/* Hero Section */}
      <div className="bg-[#e9f5ec] py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`max-w-4xl mx-auto ${
              isRTL ? "text-right" : "text-center"
            }`}
          >
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-[#1d4930]">
              {t("insights.hero.title")}
            </h1>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              {t("insights.hero.subtitle")}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-20 -mt-10 relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Insights Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {insights.map((insight) => (
                <article
                  key={insight.id}
                  className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden hover:border-gray-300 hover:shadow-sm transition-all"
                >
                  <div className="h-48 overflow-hidden">
                    <img
                      src={insight.image}
                      alt={insight.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      {/* <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                        {insight.category}
                      </span> */}
                      <span className="text-xs text-gray-600">
                        {insight.readTime}
                      </span>
                    </div>
                    <Link href={`/insights/${insight.id}`}>
                      <h3 className="text-xl font-semibold mb-3 hover:text-primary cursor-pointer text-gray-900 line-clamp-2">
                        {insight.title}
                      </h3>
                    </Link>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {insight.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {new Date(insight.date).toLocaleDateString()}
                      </span>
                      <Link
                        href={`/insights/${insight.id}`}
                        className="text-primary hover:text-primary/80 font-medium text-sm"
                      >
                        {t("common.readMore")} →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div
              className={`bg-gray-100 rounded-lg p-8 mt-16 border border-gray-200 ${
                isRTL ? "text-right" : "text-center"
              }`}
            >
              <h2 className="text-2xl font-bold mb-4 text-gray-900">
                {t("insights.newsletter.title")}
              </h2>
              <p className="text-gray-700 mb-6">
                {t("insights.newsletter.subtitle")}
              </p>
              <div className="flex max-w-md mx-auto gap-4">
                <input
                  type="email"
                  placeholder={t("insights.newsletter.placeholder")}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-primary bg-white"
                />
                <button className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors shadow-sm">
                  {t("insights.newsletter.subscribe")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helpers
function getCategoryKey(title: string) {
  if (title.toLowerCase().includes("tax")) return "tax";
  if (title.toLowerCase().includes("trust")) return "trust";
  if (title.toLowerCase().includes("entrepreneur")) return "business";
  if (title.toLowerCase().includes("free zone")) return "business";
  if (title.toLowerCase().includes("wealth")) return "investment";
  return "tax";
}

function getDateForIndex(index: number) {
  const dates = [
    "2024-04-18",
    "2024-03-28",
    "2024-02-10",
    "2024-01-15",
    "2023-12-20",
    "2023-11-30",
  ];
  return dates[index] || new Date().toISOString().split("T")[0];
}

export default InsightsScreen;
