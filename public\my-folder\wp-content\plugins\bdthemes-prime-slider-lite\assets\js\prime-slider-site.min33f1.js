!function(e,n){"use strict";var o=function(e,n){var o=e.find(".bdt-prime-slider").find(".bdt-scroll-down"),r=o.data("selector"),t=o.data("settings");o.length&&n(o).on("click",(function(e){e.preventDefault(),bdtUIkit.scroll(o,t).scrollTo(n(r))}))},r=function(e,n){var o=e.data("id"),r=e.find("[data-reveal-enable]").data("reveal-enable");if(void 0===r||"yes"!==r)return;const t=n(".reveal-active-"+o).find('[data-reveal="reveal-active"]');n(t).css({opacity:"1"});const d=e.find("[data-reveal-settings]").data("reveal-settings");let l=0;n(t).each((function(e,n){l+=80;const o=new RevealFx(n,{revealSettings:{bgColors:[d.bgColors],direction:String(d.direction),duration:Number(d.duration+l),easing:String(d.easing),onHalfway:function(e,n){e.style.opacity=1}}});!function(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};o.rootMargin=o.rootMargin||"10% 0px 0px 0px",new IntersectionObserver((function(e,r){e.forEach((function(e){e.isIntersecting&&(n(e),o.loop||r.unobserve(e.target))}))}),o).observe(e)}(n,(function(){o.reveal()}),{root:null,rootMargin:"0px",threshold:.8})})),setTimeout((()=>{const e=n(".reveal-active-"+o);var r=n(e).find(".reveal-muted");n(r).each((function(e,o){n(o).addClass("reveal-loaded"),n(o).removeClass("reveal-muted")}))}),1.3*(d.duration+l))};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.slide",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.crelly",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.meteor",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.coral",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.folio",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.zinest",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.locate",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.slice",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-dragon.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-flogia.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-mount.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-elysium.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fiestar.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-sequester.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-mercury.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-pacific.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-paranoia.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-rubix.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-storker.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-tango.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-vertex.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woocommerce.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woolamp.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-astoria.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-avatar.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-flexure.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fluent.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fortune.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-knily.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-monster.default",r),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-general.meteor",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-blog.coral",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-isolate.locate",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-woocommerce.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-fluent.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/prime-slider-astoria.default",o)}))}(jQuery,window.elementorFrontend);