<!DOCTYPE html>
<html style="margin-top: 0 !important;" dir="ltr" 
	lang="en_US" >
	
<!-- Mirrored from proactivezone.ae/quillforms/referral-earning/ by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 05 Jul 2025 12:40:26 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
		<link rel="shortcut icon" href="../../wp-content/uploads/2024/03/cropped-proactive-zone-mobile-logo.png" />
		<style>
			html, body {
				position: relative !important;
				margin-top: 0 !important;
				margin-bottom: 0 !important;
				margin-right: 0 !important;
				margin-left: 0 !important;
				padding-top: 0 !important;
				padding-bottom: 0 !important;
				padding-right: 0 !important;
				padding-left: 0 !important;
				box-sizing: border-box;
				width: 100%;
				min-width: 100%;
				max-width: 100%;
				height: 100%;
				max-height: 100%;
				min-height: 100%;
				overflow: hidden !important;

			}
			#quillforms-renderer {
				position: absolute;
				top: 0;
				bottom: 0;
				right: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
			#quillforms-renderer ~ *:not(#qf-recaptcha):not(.razorpay-container):not(.weglot-dropdown) {
				display: none !important;
			}
			* {
				box-sizing: border-box;
			}

			.qf-loader-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				background-color: linear-gradient(96deg,rgb(5,28,65) 0%,rgb(6,86,124) 63%,rgb(6,86,124) 100%);
				justify-content: center;
				width: 100%;
				height: 100%
			}

			.qf-loader-container .qf-logo-container {
				margin-bottom: 30px;
			}
			.qf-loader-container .qf-logo-container .qf-logo {
				max-width: 150px;
				max-height: 150px;
			}

			.qf-loader-container .quillforms-branding-powered-by {
				margin-top: 20px;
				font-family: "Poppins";
				display: 'none';
			}

			.qf-loader-container .quillforms-branding-powered-by a {
				text-decoration: none !important;
				color: #ffffff !important;
			}

			
			.qf-loader-container #loading-circle {
				width: 40px;
				height: 40px;
				border: 4px rgba(0,0,0,0) solid;
				border-top: 4px #ffffff solid;
				border-radius: 50%;
				animation: spin-circle .8s infinite linear;
			}

			@-webkit-keyframes spin-circle {
				from { transform: rotate(0deg); }
					to { transform: rotate(360deg); }
			}

			@keyframes spin-circle {
				from { transform: rotate(0deg); }
					to { transform: rotate(360deg); }
			}
		</style>
		<meta content="width=device-width, initial-scale=1.0, maximum-scale=1, viewport-fit=cover" name="viewport">
		<meta name="robots" content="index">
		<title>Referral &#038; Earning</title> 
							</head>
	<body>
		<div id="quillforms-renderer">
			<div class="qf-loader-container">
								<div id="loading-circle"></div>
				<!-- <div class="quillforms-branding-powered-by">
					<a href="https://quillforms.com" target="_blank">
						Powered by Quill Forms
					</a>
				</div> -->
			</div>
		</div>
		<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/hello-elementor\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>

<!--Start of Tawk.to Script (0.8.7)-->
<script id="tawk-script" type="text/javascript">
var Tawk_API = Tawk_API || {};
var Tawk_LoadStart=new Date();
(function(){
	var s1 = document.createElement( 'script' ),s0=document.getElementsByTagName( 'script' )[0];
	s1.async = true;
	s1.src = 'https://embed.tawk.to/660140431ec1082f04dad443/1hpqe62pa';
	s1.charset = 'UTF-8';
	s1.setAttribute( 'crossorigin','*' );
	s0.parentNode.insertBefore( s1, s0 );
})();
</script>
<!--End of Tawk.to Script (0.8.7)-->
<!-- Click to Chat - https://holithemes.com/plugins/click-to-chat/  v4.15 -->  
            <div class="ht-ctc ht-ctc-chat ctc-analytics ctc_wp_mobile style-2  " id="ht-ctc-chat"  
                style="display: none;  position: fixed; bottom: 03px; left: 03px;"   >
                                <div class="ht_ctc_style ht_ctc_chat_style">
                <div  style="display: flex; justify-content: center; align-items: center;  " class="ctc-analytics ctc_s_2">
    <p class="ctc-analytics ctc_cta ctc_cta_stick ht-ctc-cta  ht-ctc-cta-hover " style="padding: 0px 16px; line-height: 1.6; font-size: 15px; background-color: #25D366; color: #ffffff; border-radius:10px; margin:0 10px;  display: none; order: 1; ">WhatsApp us</p>
    <svg style="pointer-events:none; display:block; height:50px; width:50px;" width="50px" height="50px" viewBox="0 0 1024 1024">
        <defs>
        <path id="htwasqicona-chat" d="M1023.941 765.153c0 5.606-.171 17.766-.508 27.159-.824 22.982-2.646 52.639-5.401 66.151-4.141 20.306-10.392 39.472-18.542 55.425-9.643 18.871-21.943 35.775-36.559 50.364-14.584 14.56-31.472 26.812-50.315 36.416-16.036 8.172-35.322 14.426-55.744 18.549-13.378 2.701-42.812 4.488-65.648 5.3-9.402.336-21.564.505-27.15.505l-504.226-.081c-5.607 0-17.765-.172-27.158-.509-22.983-.824-52.639-2.646-66.152-5.4-20.306-4.142-39.473-10.392-55.425-18.542-18.872-9.644-35.775-21.944-50.364-36.56-14.56-14.584-26.812-31.471-36.415-50.314-8.174-16.037-14.428-35.323-18.551-55.744-2.7-13.378-4.487-42.812-5.3-65.649-.334-9.401-.503-21.563-.503-27.148l.08-504.228c0-5.607.171-17.766.508-27.159.825-22.983 2.646-52.639 5.401-66.151 4.141-20.306 10.391-39.473 18.542-55.426C34.154 93.24 46.455 76.336 61.07 61.747c14.584-14.559 31.472-26.812 50.315-36.416 16.037-8.172 35.324-14.426 55.745-18.549 13.377-2.701 42.812-4.488 65.648-5.3 9.402-.335 21.565-.504 27.149-.504l504.227.081c5.608 0 17.766.171 27.159.508 22.983.825 52.638 2.646 66.152 5.401 20.305 4.141 39.472 10.391 55.425 18.542 18.871 9.643 35.774 21.944 50.363 36.559 14.559 14.584 26.812 31.471 36.415 50.315 8.174 16.037 14.428 35.323 18.551 55.744 2.7 13.378 4.486 42.812 5.3 65.649.335 9.402.504 21.564.504 27.15l-.082 504.226z"/>
        </defs>
        <linearGradient id="htwasqiconb-chat" gradientUnits="userSpaceOnUse" x1="512.001" y1=".978" x2="512.001" y2="1025.023">
            <stop offset="0" stop-color="#61fd7d"/>
            <stop offset="1" stop-color="#2bb826"/>
        </linearGradient>
        <use xlink:href="#htwasqicona-chat" overflow="visible" style="fill: url(#htwasqiconb-chat)" fill="url(#htwasqiconb-chat)"/>
        <g>
            <path style="fill: #FFFFFF;" fill="#FFF" d="M783.302 243.246c-69.329-69.387-161.529-107.619-259.763-107.658-202.402 0-367.133 164.668-367.214 367.072-.026 64.699 16.883 127.854 49.017 183.522l-52.096 190.229 194.665-51.047c53.636 29.244 114.022 44.656 175.482 44.682h.151c202.382 0 367.128-164.688 367.21-367.094.039-98.087-38.121-190.319-107.452-259.706zM523.544 808.047h-.125c-54.767-.021-108.483-14.729-155.344-42.529l-11.146-6.612-115.517 30.293 30.834-112.592-7.259-11.544c-30.552-48.579-46.688-104.729-46.664-162.379.066-168.229 136.985-305.096 305.339-305.096 81.521.031 158.154 31.811 215.779 89.482s89.342 134.332 89.312 215.859c-.066 168.243-136.984 305.118-305.209 305.118zm167.415-228.515c-9.177-4.591-54.286-26.782-62.697-29.843-8.41-3.062-14.526-4.592-20.645 4.592-6.115 9.182-23.699 29.843-29.053 35.964-5.352 6.122-10.704 6.888-19.879 2.296-9.176-4.591-38.74-14.277-73.786-45.526-27.275-24.319-45.691-54.359-51.043-63.543-5.352-9.183-.569-14.146 4.024-18.72 4.127-4.109 9.175-10.713 13.763-16.069 4.587-5.355 6.117-9.183 9.175-15.304 3.059-6.122 1.529-11.479-.765-16.07-2.293-4.591-20.644-49.739-28.29-68.104-7.447-17.886-15.013-15.466-20.645-15.747-5.346-.266-11.469-.322-17.585-.322s-16.057 2.295-24.467 11.478-32.113 31.374-32.113 76.521c0 45.147 32.877 88.764 37.465 94.885 4.588 6.122 64.699 98.771 156.741 138.502 21.892 9.45 38.982 15.094 52.308 19.322 21.98 6.979 41.982 5.995 57.793 3.634 17.628-2.633 54.284-22.189 61.932-43.615 7.646-21.427 7.646-39.791 5.352-43.617-2.294-3.826-8.41-6.122-17.585-10.714z"/>
        </g>
        </svg></div>                </div>
            </div>
                        <span class="ht_ctc_chat_data" 
                data-no_number=""
                data-settings="{&quot;number&quot;:&quot;971523340007&quot;,&quot;pre_filled&quot;:&quot;Hello Proactive Zone\r\n\r\nContact us More Information\r\nEmail <NAME_EMAIL>\r\n&amp;#x200d;\r\nDubai office: 2303, Al Moosa Tower 2, Sheikh Zayed Road, Dubai&quot;,&quot;dis_m&quot;:&quot;show&quot;,&quot;dis_d&quot;:&quot;show&quot;,&quot;css&quot;:&quot;display: none; cursor: pointer; z-index: 99999999;&quot;,&quot;pos_d&quot;:&quot;position: fixed; bottom: 03px; left: 03px;&quot;,&quot;pos_m&quot;:&quot;position: fixed; bottom: 03px; left: 03px;&quot;,&quot;schedule&quot;:&quot;no&quot;,&quot;se&quot;:150,&quot;ani&quot;:&quot;no-animations&quot;,&quot;url_target_d&quot;:&quot;_blank&quot;,&quot;ga&quot;:&quot;yes&quot;,&quot;fb&quot;:&quot;yes&quot;,&quot;g_init&quot;:&quot;default&quot;,&quot;g_an_event_name&quot;:&quot;click to chat&quot;,&quot;pixel_event_name&quot;:&quot;Click to Chat by HoliThemes&quot;}" 
            ></span>
                    <script>
            testjstartklarCountrySelectorQueryExist();

            function testjstartklarCountrySelectorQueryExist() {
                if (window.jQuery) {
                    jQuery(window).on('elementor/frontend/init', function () {
                        if (typeof (elementor) !== "undefined") {
                            const filter_name = `elementor_pro/forms/content_template/field/phone_number_prefix_selector_form_field`;
                            elementor.hooks.addFilter(filter_name, function (inputField, item, i, settings) {
                                const widget_id = item._id;

                                let setting = "";
                                settings.form_fields.forEach(sett_obj => {
                                    if (sett_obj._id === widget_id) {
                                        setting = sett_obj;
                                    }
                                });

                                let ret_str;

                                if (setting && (typeof (setting.display_composition) !== "undefined")) {
                                    const json_text = JSON.stringify({
                                        display_composition: setting.display_composition,
                                        phone_numb_format: setting.phone_numb_format
                                    });
                                    ret_str = `<select class="startklar_country_selector" name="${item.custom_id}"  data-options="` + json_text + `"></select>`;
                                } else {
                                    ret_str = `<select class="startklar_country_selector" name="${item.custom_id}"></select>`;
                                }

                                return ret_str;
                            }, 10, 4);
                        }

                        elementorFrontend.hooks.addAction('frontend/element_ready/form.default', function (element) {
                            let t_selector_options = "";
                            const phone_prefix_selectors = document.querySelectorAll('.startklar_country_selector');
                            const selectors = jQuery(element).find('.startklar_country_selector');

                            if (phone_prefix_selectors.length) {
                                if (typeof (window.phone_number_prefix_selector_options) !== "undefined") {
                                    const setting = jQuery(".startklar_country_selector").data("options");

                                    if (typeof (setting.phone_numb_format) !== "undefined" && setting.phone_numb_format === "old_format") {
                                        t_selector_options = window.phone_number_prefix_selector_options.replace(/\(\+([0-9\s]+?)\)/gi, '(00$1)');
                                    } else if (typeof (setting.phone_numb_format) !== "undefined" && setting.phone_numb_format === "clean_format") {
                                        t_selector_options = window.phone_number_prefix_selector_options.replace(/\(\+([0-9\s]+?)\)/gi, '+$1');
                                    } else {
                                        t_selector_options = window.phone_number_prefix_selector_options;
                                    }

                                    if (typeof (setting.display_composition) !== "undefined" && (setting.display_composition.toLowerCase().indexOf("flag") === -1)) {
                                        t_selector_options = t_selector_options.replace(/data-icon="[^"]*"/gi, '');
                                    }

                                    if (typeof (setting.display_composition) !== "undefined" && (setting.display_composition.toLowerCase().indexOf("name") === -1)) {
                                        jQuery(".startklar_country_selector").addClass("hide_counties_names");

                                    }
                                }

                                jQuery(".startklar_country_selector").html(t_selector_options);

                                jQuery('.startklar_country_selector').select2({
                                    templateSelection: startklarCountrySelectorformatText,
                                    templateResult: startklarCountrySelectorRsltformatText,
                                    selectionTitleAttribute: false
                                });

                                clearSelectTitle();

                                jQuery(document).on('change.select2', () => {
                                    clearSelectTitle();
                                });

                                jQuery('.select2-selection.select2-selection--single').addClass("elementor-field-textual");
                                jQuery('.select2-selection.select2-selection--single').addClass("elementor-field");

                                jQuery(document).on('select2:open', () => {
                                    document.querySelector('.select2-search__field').focus();
                                    if (jQuery(".startklar_country_selector.hide_counties_names").length) {
                                        jQuery(".select2-container .select2-dropdown").addClass("hide_counties_names");
                                    }
                                });

                                const p_form = jQuery(".startklar_country_selector").closest("form");

                                if (typeof p_form !== "undefined") {
                                    jQuery(p_form).on('submit_success', function () {
                                        jQuery('.startklar_country_selector', this).val('').trigger("change");
                                    });
                                }
                            }

                            window.addEventListener('load', function (event) {
                                selectors.each(function (index, selector) {
                                    const default_val = jQuery(selector).data("default_val");

                                    if (default_val) {
                                        selectOptionByCountry(default_val, selector);
                                    } else {
                                        getDetectedCountry().then(function (country_en) {
                                            selectOptionByCountry(country_en, selector);
                                        });
                                    }

                                    const parent = selector.parentElement;

                                    if (isNextFieldPhoneNumberField(parent)) {
                                        const phoneField = parent.nextElementSibling.children[0]
                                        phoneField.addEventListener('input', function() {
                                            debouncedHandleAutocomplete(selector);
                                        });
                                    }

                                });
                            });

                        });
                        
                        function isNextFieldPhoneNumberField(prefixField) {
                            const nextField = prefixField.nextElementSibling;

                            return nextField && nextField.classList.contains('elementor-field-type-tel');
                        }

                        function containsPotentialPrefix(phoneNumberField) {
                            const phoneNumberValue = phoneNumberField.value;

                            const potentialPrefixPatterns = [
                                /\(\+\d+\)/, // Matches patterns like "(+380)"
                                /\+\d+\s/    // Matches patterns like "+345 "
                            ];

                            return potentialPrefixPatterns.some(function(pattern) {
                                return pattern.test(phoneNumberValue);
                            });
                        }

                        function debounce(func, delay) {
                            let timeoutId;

                            return function() {
                                clearTimeout(timeoutId);

                                timeoutId = setTimeout(() => {
                                    func.apply(this, arguments);
                                }, delay);
                            };
                        }

                        const debouncedHandleAutocomplete = debounce(function(prefixField) {
                            const phoneNumberField = prefixField.parentElement.nextElementSibling.children[0];

                            if (containsPotentialPrefix(phoneNumberField)) {
                                const phoneNumberValue = phoneNumberField.value;
                                const potentialPrefix = phoneNumberValue.match(/\(\+\d+\)|\+\d+\s/);
                                const cleanedPrefix = potentialPrefix[0].replace("(", "").replace(")", " ");

                                if (cleanedPrefix) {
                                    const prefixOption = prefixField.querySelector('option[value="' + cleanedPrefix + '"]');

                                    if (prefixOption) {
                                        jQuery(prefixOption).prop('selected', true).parent().trigger('change');
                                    }

                                    phoneNumberField.value = phoneNumberValue.replace(potentialPrefix[0], '');
                                }
                            }

                        }, 300);
                    });

                } else {
                    setTimeout(testjstartklarCountrySelectorQueryExist, 100);
                }
            }

            function startklarCountrySelectorformatText(icon) {
                let str = "";

                if (typeof icon.element !== "undefined") {
                    const phone_code = /\+\d+/g.exec(icon.text);
                    const icon_src = jQuery(icon.element).data('icon');
                    let icon_code = '';

                    if (typeof icon_src !== "undefined" && icon_src.length) {
                        icon_code = '<img src="' + icon_src + '">';
                    }

                    if (typeof phone_code !== "undefined" && phone_code != null && phone_code.length) {
                        str = '<span>' + icon_code + phone_code[0] + '<span>';
                    }
                }
                return jQuery(str);
            }

            function startklarCountrySelectorRsltformatText(icon) {
                let str;

                if (typeof icon.element !== "undefined") {
                    const icon_src = jQuery(icon.element).data('icon');
                    let icon_code = '';

                    if (typeof icon_src !== "undefined" && icon_src.length) {
                        icon_code = '<img src="' + icon_src + '">';
                    }
                    str = '<span>' + icon_code + icon.text + '</span>';
                }

                return jQuery(str);
            }

            function getDetectedCountry() {
                return new Promise((resolve, reject) => {
                    jQuery.post("https://proactivezone.ae/wp-admin/admin-ajax.php?action=startklar_country_selector_process", function (data) {
                        const data_country = data["country"];
                        let country_en = null;

                        if (typeof data_country !== "undefined") {
                            jQuery('.startklar_country_selector > option').each(function () {
                                const optionCountry = jQuery(this).data('country_en');
                                if (optionCountry === data_country) {
                                    country_en = optionCountry;
                                    return false;
                                }
                            });
                        }

                        if (country_en !== null) {
                            resolve(country_en);
                        } else {
                            reject("Country code not found");
                        }
                    }, "json");
                });
            }

            function selectOptionByCountry(countryName, selector) {
                jQuery(selector).find('option').each(function () {
                    const optionCountry = jQuery(this).data('country_en');
                    if (optionCountry === countryName) {
                        jQuery(this).prop('selected', true).parent().trigger('change');

                        return false;
                    }
                });
            }

            function clearSelectTitle() {
                const select_items = jQuery(".select2.select2-container  .select2-selection.select2-selection--single > .select2-selection__rendered");

                if (select_items.length > 0) {
                    jQuery(select_items).each(function (index) {
                        const select_itm = jQuery(this);
                        const title = select_itm.attr("title");

                        if (typeof title !== 'undefined' && title) {
                            const rslt = title.match(/([^<]+)<span[^>]*>([^<]+)<\/span>/ism);

                            if (rslt !== null) {
                                const new_title = rslt[1] + " " + rslt[2];
                                select_itm.attr("title", new_title);
                            }
                        }
                    });
                }
            }
        </script>
                <script>
            testjStartklarDropZoneJQueryExist();

            function testjStartklarDropZoneJQueryExist() {
                if (window.jQuery) {
                    jQuery(window).on('elementor/frontend/init', function () {
                        if (typeof (elementor) !== "undefined") {
                            elementor.hooks.addFilter('elementor_pro/forms/content_template/field/drop_zone_form_field', function (inputField, item, i, settings) {
                                const itemClasses = item.css_classes;
                                const fieldName = 'form_field_';
                                const dropzone_hash = MD5(Date.now()).substring(0, 10);
                                let button_message;

                                if (typeof (item["button_message"]) == "undefined" || !item["button_message"]) {
                                    button_message = "Drop files here to upload";
                                } else {
                                    button_message = item["button_message"];
                                }

                                button_message = ' data-serialize-arr=\'{"button_message":"' + button_message + '"}\' ';
                                const ret_str = `
                            <div class="wrap_dropzone_container">
                                <div class="dropzone dropzone_container" ` + button_message + ` ></div>
                                <input class="dropzone_hash"  type="hidden" name="${fieldName}" value="${dropzone_hash}"></input>
                            </div>`;

                                return ret_str;
                            }, 10, 4);
                        }

                        elementorFrontend.hooks.addAction('frontend/element_ready/form.default', function () {
                            window.loop_cntr = 0;

                            jQuery('head').append('<link rel="stylesheet" href="https://proactivezone.ae/wp-content/plugins/startklar-elmentor-forms-extwidgets/assets/dropzone/dropzone.min.css" type="text/css" />');
                            Dropzone.autoDiscover = false;
                            searchDropZoneContainer();
                        });
                    });
                } else {
                    setTimeout(testjStartklarDropZoneJQueryExist, 100);
                }
            }

            function searchDropZoneContainer() {
                if (window.loop_cntr > 100) {
                    return;
                }

                if (jQuery(".dropzone_container").length) {
                    jQuery(".dropzone_container").each(function (index) {
                        if (!jQuery(this).hasClass("dz-clickable")) {
                            let settings = jQuery(this).data("serializeArr");
                            const dropzoneHash = settings.dropzone_hash;

                            const default_settings = {
                                allowed_file_types_for_upload: "*.jpg, *.pdf",
                                files_amount: 1,
                                maximum_upload_file: "10",
                                uploaded_files_storage_path: "",
                                button_message: "Ziehen Sie Ihre Dateien hierher<br/> und legen Sie sie dort ab oder klicken Sie,<br/> um eine Datei auszuwählen"
                            }
                            settings = Object.assign(default_settings, settings);

                            jQuery(this).dropzone({
                                url: "https://proactivezone.ae/wp-admin/admin-ajax.php?action=startklar_drop_zone_upload_process",
                                addRemoveLinks: true,
                                dictDefaultMessage: settings["button_message"],
                                dictRemoveFile: '<i class="dashicons dashicons-no"></i>',
                                dictCancelUpload: '<i class="dashicons dashicons-no"></i>',
                                init: function () {
                                    dzClosure = this;
                                    this.on("sending", function (file, xhr, formData) {
                                        function appendUniqueHash() {
                                            let hashKey;
                                            do {
                                                const randomSuffix = Math.floor(Math.random() * 1000);
                                                hashKey = `hash_${randomSuffix}`;
                                            } while (formData.has(hashKey));
                                            formData.append(hashKey, dropzoneHash);
                                        }

                                        appendUniqueHash();
                                    });
                                },
                                removedfile: function (file) {
                                    const fileName = file.name;
                                    const input_val = jQuery(this.element).closest("form").find("input.dropzone_hash").val();
                                    const json_obj = JSON.parse(input_val);
                                    const hash = json_obj["dropzone_hash"];

                                    jQuery.post("../../wp-admin/admin-ajax238f.html?action=startklar_drop_zone_upload_process",
                                        {mode: "remove", hash: hash, fileName: fileName},
                                        function (data) {
                                        }
                                    );

                                    if (file.previewElement != null && file.previewElement.parentNode != null) {
                                        file.previewElement.parentNode.removeChild(file.previewElement);
                                    }

                                    return this._updateMaxFilesReachedClass();
                                },

                                maxFilesize: settings["maximum_upload_file"],
                                maxFiles: settings["files_amount"],
                                acceptedFiles: settings["allowed_file_types_for_upload"]
                            });
                        }

                        const p_form = this.closest("form");
                        if (typeof p_form !== "undefined") {
                            jQuery(p_form).on('submit_success', function () {
                                jQuery(this).find(".dropzone_container").each(function (index, element) {
                                    const objDZ = Dropzone.forElement(this);
                                    objDZ.emit("reset");
                                    objDZ.removeAllFiles(true);
                                })
                            });
                        }
                    });
                } else {
                    setTimeout(searchDropZoneContainer, 100);
                    window.loop_cntr++;
                }
            }

            const MD5 = function (d) {
                result = M(V(Y(X(d), 8 * d.length)));
                return result.toLowerCase()
            };

            function M(d) {
                for (var _, m = "0123456789ABCDEF", f = "", r = 0; r < d.length; r++) _ = d.charCodeAt(r), f += m.charAt(_ >>> 4 & 15) + m.charAt(15 & _);
                return f
            }

            function X(d) {
                for (var _ = Array(d.length >> 2), m = 0; m < _.length; m++) _[m] = 0;
                for (m = 0; m < 8 * d.length; m += 8) _[m >> 5] |= (255 & d.charCodeAt(m / 8)) << m % 32;
                return _
            }

            function V(d) {
                for (var _ = "", m = 0; m < 32 * d.length; m += 8) _ += String.fromCharCode(d[m >> 5] >>> m % 32 & 255);
                return _
            }

            function Y(d, _) {
                d[_ >> 5] |= 128 << _ % 32, d[14 + (_ + 64 >>> 9 << 4)] = _;
                for (var m = 1732584193, f = -271733879, r = -1732584194, i = 271733878, n = 0; n < d.length; n += 16) {
                    var h = m, t = f, g = r, e = i;
                    f = md5_ii(f = md5_ii(f = md5_ii(f = md5_ii(f = md5_hh(f = md5_hh(f = md5_hh(f = md5_hh(f = md5_gg(f = md5_gg(f = md5_gg(f = md5_gg(f = md5_ff(f = md5_ff(f = md5_ff(f = md5_ff(f, r = md5_ff(r, i = md5_ff(i, m = md5_ff(m, f, r, i, d[n + 0], 7, -680876936), f, r, d[n + 1], 12, -389564586), m, f, d[n + 2], 17, 606105819), i, m, d[n + 3], 22, -1044525330), r = md5_ff(r, i = md5_ff(i, m = md5_ff(m, f, r, i, d[n + 4], 7, -176418897), f, r, d[n + 5], 12, 1200080426), m, f, d[n + 6], 17, -1473231341), i, m, d[n + 7], 22, -45705983), r = md5_ff(r, i = md5_ff(i, m = md5_ff(m, f, r, i, d[n + 8], 7, 1770035416), f, r, d[n + 9], 12, -1958414417), m, f, d[n + 10], 17, -42063), i, m, d[n + 11], 22, -1990404162), r = md5_ff(r, i = md5_ff(i, m = md5_ff(m, f, r, i, d[n + 12], 7, 1804603682), f, r, d[n + 13], 12, -40341101), m, f, d[n + 14], 17, -1502002290), i, m, d[n + 15], 22, 1236535329), r = md5_gg(r, i = md5_gg(i, m = md5_gg(m, f, r, i, d[n + 1], 5, -165796510), f, r, d[n + 6], 9, -1069501632), m, f, d[n + 11], 14, 643717713), i, m, d[n + 0], 20, -373897302), r = md5_gg(r, i = md5_gg(i, m = md5_gg(m, f, r, i, d[n + 5], 5, -701558691), f, r, d[n + 10], 9, 38016083), m, f, d[n + 15], 14, -660478335), i, m, d[n + 4], 20, -405537848), r = md5_gg(r, i = md5_gg(i, m = md5_gg(m, f, r, i, d[n + 9], 5, 568446438), f, r, d[n + 14], 9, -1019803690), m, f, d[n + 3], 14, -187363961), i, m, d[n + 8], 20, 1163531501), r = md5_gg(r, i = md5_gg(i, m = md5_gg(m, f, r, i, d[n + 13], 5, -1444681467), f, r, d[n + 2], 9, -51403784), m, f, d[n + 7], 14, 1735328473), i, m, d[n + 12], 20, -1926607734), r = md5_hh(r, i = md5_hh(i, m = md5_hh(m, f, r, i, d[n + 5], 4, -378558), f, r, d[n + 8], 11, -2022574463), m, f, d[n + 11], 16, 1839030562), i, m, d[n + 14], 23, -35309556), r = md5_hh(r, i = md5_hh(i, m = md5_hh(m, f, r, i, d[n + 1], 4, -1530992060), f, r, d[n + 4], 11, 1272893353), m, f, d[n + 7], 16, -155497632), i, m, d[n + 10], 23, -1094730640), r = md5_hh(r, i = md5_hh(i, m = md5_hh(m, f, r, i, d[n + 13], 4, 681279174), f, r, d[n + 0], 11, -358537222), m, f, d[n + 3], 16, -722521979), i, m, d[n + 6], 23, 76029189), r = md5_hh(r, i = md5_hh(i, m = md5_hh(m, f, r, i, d[n + 9], 4, -640364487), f, r, d[n + 12], 11, -421815835), m, f, d[n + 15], 16, 530742520), i, m, d[n + 2], 23, -995338651), r = md5_ii(r, i = md5_ii(i, m = md5_ii(m, f, r, i, d[n + 0], 6, -198630844), f, r, d[n + 7], 10, 1126891415), m, f, d[n + 14], 15, -1416354905), i, m, d[n + 5], 21, -57434055), r = md5_ii(r, i = md5_ii(i, m = md5_ii(m, f, r, i, d[n + 12], 6, 1700485571), f, r, d[n + 3], 10, -1894986606), m, f, d[n + 10], 15, -1051523), i, m, d[n + 1], 21, -2054922799), r = md5_ii(r, i = md5_ii(i, m = md5_ii(m, f, r, i, d[n + 8], 6, 1873313359), f, r, d[n + 15], 10, -30611744), m, f, d[n + 6], 15, -1560198380), i, m, d[n + 13], 21, 1309151649), r = md5_ii(r, i = md5_ii(i, m = md5_ii(m, f, r, i, d[n + 4], 6, -145523070), f, r, d[n + 11], 10, -1120210379), m, f, d[n + 2], 15, 718787259), i, m, d[n + 9], 21, -343485551), m = safe_add(m, h), f = safe_add(f, t), r = safe_add(r, g), i = safe_add(i, e)
                }
                return Array(m, f, r, i)
            }

            function md5_cmn(d, _, m, f, r, i) {
                return safe_add(bit_rol(safe_add(safe_add(_, d), safe_add(f, i)), r), m)
            }

            function md5_ff(d, _, m, f, r, i, n) {
                return md5_cmn(_ & m | ~_ & f, d, _, r, i, n)
            }

            function md5_gg(d, _, m, f, r, i, n) {
                return md5_cmn(_ & f | m & ~f, d, _, r, i, n)
            }

            function md5_hh(d, _, m, f, r, i, n) {
                return md5_cmn(_ ^ m ^ f, d, _, r, i, n)
            }

            function md5_ii(d, _, m, f, r, i, n) {
                return md5_cmn(m ^ (_ | ~f), d, _, r, i, n)
            }

            function safe_add(d, _) {
                var m = (65535 & d) + (65535 & _);
                return (d >> 16) + (_ >> 16) + (m >> 16) << 16 | 65535 & m
            }

            function bit_rol(d, _) {
                return d << _ | d >>> 32 - _
            }
        </script>
                <script>
            testStartklarHoneyPotJQueryExist();
            function testStartklarHoneyPotJQueryExist() {
                if (window.jQuery) {
                    jQuery('.elementor-field-type-startklar_hp_form_field  input').removeAttr('required');
                }else{
                    setTimeout(testStartklarHoneyPotJQueryExist, 100);
                }
            }
        </script> 			<script>
				;
				(function($, w) {
					'use strict';
					let $window = $(w);

					$(document).ready(function() {

						let isEnable = "";
						let isEnableLazyMove = "";
						let speed = isEnableLazyMove ? '0.7' : '0.2';

						if( !isEnable ) {
							return;
						}

						if (typeof haCursor == 'undefined' || haCursor == null) {
							initiateHaCursorObject(speed);
						}

						setTimeout(function() {
							let targetCursor = $('.ha-cursor');
							if (targetCursor) {
								if (!isEnable) {
									$('body').removeClass('hm-init-default-cursor-none');
									$('.ha-cursor').addClass('ha-init-hide');
								} else {
									$('body').addClass('hm-init-default-cursor-none');
									$('.ha-cursor').removeClass('ha-init-hide');
								}
							}
						}, 500);

					});

				}(jQuery, window));
			</script>
		
					<script>
				const lazyloadRunObserver = () => {
					const lazyloadBackgrounds = document.querySelectorAll( `.e-con.e-parent:not(.e-lazyloaded)` );
					const lazyloadBackgroundObserver = new IntersectionObserver( ( entries ) => {
						entries.forEach( ( entry ) => {
							if ( entry.isIntersecting ) {
								let lazyloadBackground = entry.target;
								if( lazyloadBackground ) {
									lazyloadBackground.classList.add( 'e-lazyloaded' );
								}
								lazyloadBackgroundObserver.unobserve( entry.target );
							}
						});
					}, { rootMargin: '200px 0px 200px 0px' } );
					lazyloadBackgrounds.forEach( ( lazyloadBackground ) => {
						lazyloadBackgroundObserver.observe( lazyloadBackground );
					} );
				};
				const events = [
					'DOMContentLoaded',
					'elementor/lazyload/observe',
				];
				events.forEach( ( event ) => {
					document.addEventListener( event, lazyloadRunObserver );
				} );
			</script>
			<div
				id="hustle-popup-id-4"
				class="hustle-ui hustle-popup hustle-palette--gray_slate hustle_module_id_4 module_id_4  "
				
			data-id="4"
			data-render-id="0"
			data-tracking="enabled"
			
				role="dialog"
				aria-modal="true"
				data-intro="fadeIn"
				data-outro="fadeOut"
				data-overlay-close="1"
				data-close-delay="false"
				
				style="opacity: 0;"
			><div class="hustle-popup-mask hustle-optin-mask" aria-hidden="true"></div><div class="hustle-popup-content"><div class="hustle-optin hustle-optin--default"><div class="hustle-success" data-close-delay="false" style="display: none;"><span class="hustle-icon-check" aria-hidden="true"></span></div><div class="hustle-layout"><div class="hustle-main-wrapper"><button class="hustle-button-icon hustle-button-close has-background">
			<span class="hustle-icon-close" aria-hidden="true"></span>
			<span class="hustle-screen-reader">Close this module</span>
		</button><div class="hustle-layout-body"><div class="hustle-layout-content hustle-layout-position--above"><div class="hustle-image hustle-image-fit--cover" aria-hidden="true"><img src="../../wp-content/uploads/2024/06/Are-you-looking-to-open-a-business-in-the-UAE-2-01.svg" alt="" class="hustle-image-position--centerbottom" /></div></div><form class="hustle-layout-form" novalidate="novalidate"><div class="hustle-form"><div class="hustle-form-fields hustle-proximity-separated"><div class="hustle-field hustle-field-required "><input id="hustle-field-name-module-4" type="name" class="hustle-input " name="name" value="" aria-labelledby="hustle-field-name-module-4-label" data-validate="" data-required-error="Your name is required." /><span class="hustle-input-label" aria-hidden="true" style="flex-flow: row nowrap;"><span>Full Name</span></span></div><div class="hustle-field hustle-field-required "><input id="hustle-field-email-module-4" type="email" class="hustle-input " name="email" value="" aria-labelledby="hustle-field-email-module-4-label" data-validate="1" data-required-error="Email field is required." data-validation-error="Please enter a valid email." /><span class="hustle-input-label" aria-hidden="true" style="flex-flow: row nowrap;"><span>Enter your email address</span></span></div><div class="hustle-field hustle-field-required "><input id="hustle-field-phone-module-4" type="text" class="hustle-input " name="phone" value="" aria-labelledby="hustle-field-phone-module-4-label" data-validate="" data-required-error="Your phone is required." data-type="phone"/><span class="hustle-input-label" aria-hidden="true" style="flex-flow: row nowrap;"><span>Enter Your Phone No</span></span></div><div class="hustle-field "><input id="hustle-field-Message-module-4" type="text" class="hustle-input " name="Message" value="" aria-labelledby="hustle-field-Message-module-4-label" data-validate="" /><span class="hustle-input-label" aria-hidden="true" style="flex-flow: row nowrap;"><span>Enter Your Message</span></span></div><button class="hustle-button hustle-button-submit " aria-live="polite" data-loading-text="Form is being submitted, please wait a bit."><span class="hustle-button-text">Send</span><span class="hustle-icon-loader hustle-loading-icon" aria-hidden="true"></span></button></div></div><input type="hidden" name="hustle_module_id" value="4"><input type="hidden" name="post_id" value="7858"><div class="hustle-error-message" style="display: none;" data-default-error="Something went wrong, please try again."></div></form></div></div><div class="hustle-layout-footer"><p class="hustle-nsa-link"><a href="#">Thanks, I’m not interested</a></p></div></div></div></div></div><link rel='stylesheet' id='quillforms-renderer-core-css' href='../../wp-content/plugins/quillforms/build/renderer-core/style8d74.css?ver=1736234947' media='all' />
<link rel='stylesheet' id='quillforms-blocklib-short-text-block-renderer-style-css' href='../../wp-content/plugins/quillforms/build/blocklib-short-text-block/renderer8d74.css?ver=1736234947' media='all' />
<link rel='stylesheet' id='quillforms-blocklib-email-block-renderer-style-css' href='../../wp-content/plugins/quillforms/build/blocklib-email-block/renderer8d74.css?ver=1736234947' media='all' />
<link rel='stylesheet' id='quillforms-blocklib-phone-block-renderer-style-css' href='../../wp-content/plugins/quillforms-phoneblock/build/renderer/style5e1b.css?ver=1718274015' media='all' />
<style id="quillforms-renderer-load-font-css" media="all">@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 100;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiAyp8kv8JHgFVrJJLmE0tCMPc.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLmv1pVF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLm21lVF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiGyp8kv8JHgFVrJJLucHtF.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 500;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLmg1hVF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLmr19VF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLmy15VF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 800;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLm111VF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: italic;
  font-weight: 900;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiDyp8kv8JHgFVrJJLm81xVF9eL.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiGyp8kv8JHgFVrLPTucHtF.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLFj_Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLDz8Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrJJfedw.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLGT9Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLEj6Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLDD4Z1xlEA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(../../fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLBT5Z1xlEA.ttf) format('truetype');
}
</style>
<link rel='stylesheet' id='hustle_icons-css' href='../../wp-content/plugins/wordpress-popup/assets/hustle-ui/css/hustle-icons.min34ee.css?ver=7.8.6' media='all' />
<link rel='stylesheet' id='hustle_global-css' href='../../wp-content/plugins/wordpress-popup/assets/hustle-ui/css/hustle-global.min34ee.css?ver=7.8.6' media='all' />
<link rel='stylesheet' id='hustle_optin-css' href='../../wp-content/plugins/wordpress-popup/assets/hustle-ui/css/hustle-optin.min34ee.css?ver=7.8.6' media='all' />
<link rel='stylesheet' id='hustle_popup-css' href='../../wp-content/plugins/wordpress-popup/assets/hustle-ui/css/hustle-popup.min34ee.css?ver=7.8.6' media='all' />
<link rel='stylesheet' id='hustle-fonts-css' href='https://fonts.bunny.net/css?family=Open+Sans%3Aregular%2C700&amp;display=swap&amp;ver=1.0' media='all' />
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/vendor/react.mine1ab.js?ver=********" id="react-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/vendor/react-dom.mine1ab.js?ver=********" id="react-dom-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/escape-html.min3a9d.js?ver=6561a406d2d232a6fbd2" id="wp-escape-html-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/element.minfa0f.js?ver=a4eeeadd23c0d7ab1d2d" id="wp-element-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/hooks.min4fdd.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/i18n.minc33c.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" id="wp-i18n-js-after">
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/lib/vendor/emotion.min10f0.js?ver=1.8.6" id="emotion-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/vendor/lodash.mind1d1.js?ver=4.17.21" id="lodash-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" id="lodash-js-after">
window.lodash = _.noConflict();
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/config/index516e.js?ver=88916b82f2cf2ca068e6" id="quillforms-config-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" id="quillforms-config-js-after">
qf.config.default.setAdminUrl("../../wp-admin/index.html");qf.config.default.setFormId(7858);qf.config.default.setFormUrl("index.html");
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/utils/index6356.js?ver=4bc4ef22fe17d92e6ab2" id="quillforms-utils-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/vendor/react-jsx-runtime.mincb06.js?ver=18.3.1" id="react-jsx-runtime-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/deprecated.min0a8b.js?ver=e1f84915c5e8ae38964c" id="wp-deprecated-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/dom.minbf15.js?ver=f3a673a30f968c8fa314" id="wp-dom-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/is-shallow-equal.mincb09.js?ver=e0f9f1d78d83f5196979" id="wp-is-shallow-equal-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/keycodes.min8cc0.js?ver=034ff647a54b018581d3" id="wp-keycodes-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/priority-queue.minc7db.js?ver=9c21c957c7e50ffdbf48" id="wp-priority-queue-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/compose.minb1f8.js?ver=84bcf832a5c99203f3db" id="wp-compose-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/private-apis.mine8e0.js?ver=0f8478f1ba7e0eea562b" id="wp-private-apis-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/redux-routine.min29d8.js?ver=8bb92d45458b29590f53" id="wp-redux-routine-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/data.mindba4.js?ver=fe6c4835cd00e12493c3" id="wp-data-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" id="wp-data-js-after">
( function() {
	var userId = 0;
	var storageKey = "WP_DATA_USER_" + userId;
	wp.data
		.use( wp.data.plugins.persistence, { storageKey: storageKey } );
} )();
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/blocks/indexcda5.js?ver=b95586b608e1da1a6fd7" id="quillforms-blocks-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" id="quillforms-blocks-js-after">
qf.blocks.registerBlockType("date",{"attributes":{"format":{"type":"string","enum":["MMDDYYYY","DDMMYYYY","YYYYMMDD"],"default":"MMDDYYYY"},"separator":{"type":"string","enum":["\/","-","."],"default":"\/"}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":false,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("dropdown",{"attributes":{"choices":{"type":"array","items":{"type":"object","properties":{"value":{"type":"string"},"label":{"type":"string"}}},"default":[{"value":"123e45z7o89b","label":"Choice 1"}]}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":true,"payments":true,"points":true,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not"]});
qf.blocks.registerBlockType("email",{"attributes":{"restrictDomains":{"type":"boolean","default":false},"allowedDomains":{"type":"array","default":[]}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":true,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","starts_with","contains","ends_with","not_contains"]});
qf.blocks.registerBlockType("group",{"attributes":[],"supports":{"editable":false,"required":false,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":true,"logicConditions":false,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":true,"correctAnswers":false},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("legal",{"attributes":{"yesLabel":{"type":"string","description":"The label for the 'yes' option","default":"Yes"},"noLabel":{"type":"string","description":"The label for the 'no' option","default":"No"}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":true,"payments":false,"points":true,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not"]});
qf.blocks.registerBlockType("long-text",{"attributes":{"minCharacters":{"type":["boolean","number"],"default":false},"setMaxCharacters":{"type":"boolean","default":false},"maxCharacters":{"type":"number","multipleOf":1}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":true,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","starts_with","ends_with","contains","not_contains"]});
qf.blocks.registerBlockType("multiple-choice",{"attributes":{"choices":{"type":"array","items":{"type":"object","properties":{"value":{"type":"string"},"label":{"type":"string"}}},"default":[{"value":"124e4567e89b","label":"Choice 1"}]},"max":{"type":["number","boolean"],"default":false},"min":{"type":["number","boolean"],"default":false},"verticalAlign":{"type":"boolean","default":false},"multiple":{"type":"boolean"}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":true,"payments":true,"points":true,"innerBlocks":false,"correctAnswers":true},"logicalOperators":["is","is_not"]});
qf.blocks.registerBlockType("number",{"attributes":{"setMax":{"type":"boolean","default":false},"max":{"type":"number","default":0},"setMin":{"type":"boolean","default":false},"min":{"type":"number","default":0}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":true,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":true,"points":false,"innerBlocks":false,"correctAnswers":false,"numeric":true},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("short-text",{"attributes":{"minCharacters":{"type":["boolean","number"],"default":false},"setMaxCharacters":{"type":"boolean","default":false},"maxCharacters":{"type":"number","multipleOf":1}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":true,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","starts_with","ends_with","contains","not_contains"]});
qf.blocks.registerBlockType("slider",{"attributes":{"min":{"type":"number","default":0},"max":{"type":"number","default":100},"step":{"type":"number","default":10},"prefix":{"type":"string","default":""},"suffix":{"type":"string","default":""},"marks":{"type":"string","default":"no"},"customMarks":{"type":"array","default":[{"label":"","value":""}]}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":false,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":true,"points":false,"innerBlocks":false,"correctAnswers":false,"numeric":true},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("statement",{"attributes":{"buttonText":{"type":"string","default":"Continue"},"quotationMarks":{"type":"boolean","default":true}},"supports":{"editable":false,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("website",{"attributes":[],"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":true,"placeholder":true,"description":true,"logic":true,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","starts_with","contains","ends_with","not_contains"]});
qf.blocks.registerBlockType("welcome-screen",{"attributes":{"buttonText":{"type":"string","default":"Let's start!"}},"supports":{"editable":false,"required":false,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":false,"logicConditions":true,"theme":false,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","greater_than","lower_than"]});
qf.blocks.registerBlockType("phone",{"attributes":{"defaultCountry":{"type":"string","default":"us"},"disableOtherCountries":{"type":"boolean","default":false}},"supports":{"editable":true,"required":true,"attachment":true,"defaultValue":false,"placeholder":false,"description":true,"logic":false,"logicConditions":true,"theme":true,"choices":false,"payments":false,"points":false,"innerBlocks":false,"correctAnswers":false},"logicalOperators":["is","is_not","greater_than","lower_than"]});
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/payment-gateways/index433b.js?ver=1142bc380737d82a2c2b" id="quillforms-payment-gateways-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/autop.min33af.js?ver=9fb50649848277dd318d" id="wp-autop-js"></script>
<script src="../../wp-content/plugins/quillforms/build/renderer-core/index9eb1.js?ver=6a0664daa8b51f5434c6" id="quillforms-renderer-core-js"></script>
<script id="quillforms-renderer-core-js-after">
window.qfRender = {"ajaxurl":"https:\/\/proactivezone.ae\/wp-admin\/admin-ajax.php","formObject":{"blocks":[{"id":"qwrjjc5ph","name":"group","attributes":{"nextBtnLabel":false,"classnames":"","attachment":[],"description":"Please provide the details of the person you are referring:","label":"Referral Details","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"},"innerBlocks":[{"id":"3917on38u","name":"short-text","attributes":{"minCharacters":false,"setMaxCharacters":false,"required":true,"nextBtnLabel":false,"classnames":"","placeholder":"Enter Referral Full Name","defaultValue":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}},{"id":"9jf6682us","name":"email","attributes":{"required":false,"nextBtnLabel":false,"classnames":"","placeholder":"Referral Email Address","defaultValue":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}},{"id":"smk1ykju4","name":"phone","attributes":{"defaultCountry":"ae","disableOtherCountries":false,"required":false,"nextBtnLabel":false,"classnames":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}}]},{"id":"b0svm0o1j","name":"group","attributes":{"nextBtnLabel":false,"classnames":"","attachment":[],"description":"This is a required question.\n\nEnter your information below to track your referrals.","label":"Kindly provide your details.","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"},"innerBlocks":[{"id":"udfid2g7d","name":"short-text","attributes":{"minCharacters":false,"setMaxCharacters":false,"required":true,"nextBtnLabel":false,"classnames":"","placeholder":"Enter Your Full Name","defaultValue":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}},{"id":"o790z20f2","name":"email","attributes":{"required":false,"nextBtnLabel":false,"classnames":"","placeholder":"Enter Your Email Address","defaultValue":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}},{"id":"w1vul1xfr","name":"phone","attributes":{"defaultCountry":"ae","disableOtherCountries":false,"required":false,"nextBtnLabel":false,"classnames":"","description":"","label":"","customHTML":"","layout":"stack","attachmentFocalPoint":{"x":0.5,"y":0.5},"attachmentFancyBorderRadius":false,"attachmentBorderRadius":"0px","attachmentMaxWidth":"none"}}]}],"messages":{"label.button.ok":"OK","label.hintText.enter":"press <strong>Enter \u21b5<\/strong>","label.hintText.multipleSelection":"Choose as many as you like","block.dropdown.placeholder":"Type or select an option","block.dropdown.noSuggestions":"No Suggestions!","block.shortText.placeholder":"Type your answer here","block.longText.placeholder":"Type your answer here","block.longText.hint":"<strong>Shift \u21e7 + Enter \u21b5<\/strong> to make a line break","block.longText.touchHint":"<strong> Enter \u21b5<\/strong> to make a line break","block.number.placeholder":"Type your answer here","block.email.placeholder":"Type your email here","block.defaultThankYouScreen.label":"Thanks for filling this in.\n\n We will contact you soon","label.correct":"Correct","label.incorrect":"Incorrect","label.yourAnswer":"Your answer","label.answersExplanation":"Answers explanation","label.hintText.key":"Key","label.yes.default":"Yes","label.no.default":"No","label.progress.percent":"{{progress:percent}}% completed","label.errorAlert.required":"This field is required!","label.errorAlert.date":"Invalid date!","label.errorAlert.number":"Numbers only!","label.errorAlert.selectionRequired":"Please make at least one selection!","label.errorAlert.email":"Invalid email!","label.errorAlert.emailRestrictedDomains":"Only email addresses from the following domains are allowed: {{attribute:allowedDomains}}","label.errorAlert.url":"Invalid url!","label.errorAlert.range":"Please enter a number between {{attribute:min}} and {{attribute:max}}","label.errorAlert.minNum":"Please enter a number greater than {{attribute:min}}","label.errorAlert.maxNum":"Please enter a number lower than {{attribute:max}}","label.errorAlert.maxCharacters":"Maximum characters reached!","label.errorAlert.minCharacters":"Please insert more than {{attribute:minCharacters}} characters!","label.errorAlert.minChoices":"Please select at least {{attribute:min}} choices!","label.errorAlert.maxChoices":"Maximum choices to select is {{attribute:max}}!","label.submitBtn":"Submit","label.errorAlert.noConnection":"Can't connect to the server right now!","label.errorAlert.serverError":"Server error!","label.errorAlert.phone":"Invalid phone number!"},"theme":{"font":"Poppins","fontSize":{"lg":"20px","sm":"16px"},"fontLineHeight":{"lg":"28px","sm":"19px"},"backgroundColor":"linear-gradient(96deg,rgb(5,28,65) 0%,rgb(6,86,124) 63%,rgb(6,86,124) 100%)","backgroundImageFocalPoint":{"x":0.5,"y":0.5},"logo":[],"typographyPreset":"md","questionsLabelFont":"Poppins","questionsLabelFontSize":{"lg":"24px","sm":"20px"},"questionsLabelLineHeight":{"lg":"19px","sm":"22px"},"questionsDescriptionFont":"Poppins","questionsDescriptionFontSize":{"lg":"15px","sm":"15px"},"questionsDescriptionLineHeight":{"lg":"28px","sm":"24px"},"questionsColor":"#ffffff","answersColor":"#ffffff","textInputAnswers":{"lg":"20px","sm":"18px"},"buttonsFontColor":"#ffffff","buttonsFontSize":{"lg":"20px","sm":"16px"},"buttonsPadding":{"top":{"lg":"9px","sm":"9px"},"bottom":{"lg":"9px","sm":"9px"},"left":{"lg":"23px","sm":"23px"},"right":{"lg":"20px","sm":"20px"}},"buttonsBgColor":"rgba(255, 0, 0, 1)","buttonsBorderRadius":25,"buttonsBorderWidth":0,"buttonsBorderColor":"#f90000","errorsFontColor":"#af0404","errorsBgColor":"#f7e6e6","progressBarFillColor":"#ff0000","progressBarBgColor":"#ffffff","formFooterBgColor":{"lg":"transparent","sm":"transparent"}},"themesList":[{"id":1,"title":"Cost Calculator ","properties":{"font":"Poppins","fontSize":{"lg":"20px","sm":"16px"},"fontLineHeight":{"lg":"24px","sm":"24px"},"backgroundColor":"","backgroundImageFocalPoint":{"x":0.460000000000000019984014443252817727625370025634765625,"y":0.320000000000000006661338147750939242541790008544921875},"logo":[],"typographyPreset":"md","questionsLabelFont":"Inherit","questionsLabelFontSize":{"lg":"22px","sm":"20px"},"questionsLabelLineHeight":{"lg":"24px","sm":"28px"},"questionsDescriptionFont":"Inherit","questionsDescriptionFontSize":{"lg":"18px","sm":"16px"},"questionsDescriptionLineHeight":{"lg":"20px","sm":"24px"},"questionsColor":"#ffffff","answersColor":"#ffffff","textInputAnswers":{"lg":"18px","sm":"24px"},"buttonsFontColor":"#fff","buttonsFontSize":{"lg":"15px","sm":"16px"},"buttonsPadding":{"top":{"lg":"9px","sm":"9px"},"bottom":{"lg":"9px","sm":"9px"},"left":{"lg":"23px","sm":"23px"},"right":{"lg":"20px","sm":"20px"}},"buttonsBgColor":"rgba(207, 46, 46, 1)","buttonsBorderRadius":25,"buttonsBorderWidth":0,"buttonsBorderColor":"#ff0000","errorsFontColor":"#ff0000","errorsBgColor":"rgba(255, 255, 255, 1)","progressBarFillColor":"#ff0000","progressBarBgColor":"#ffffff","formFooterBgColor":{"lg":"transparent","sm":"transparent"},"backgroundImage":"https:\/\/proactivezone.ae\/wp-content\/uploads\/2024\/05\/cost-calculator-design-.jpg","answersMargin":{"top":{"lg":"24px","sm":"24px"},"bottom":{"lg":"16px","sm":"16px"},"right":{"lg":"0px","sm":"0px"},"left":{"lg":"0px","sm":"0px"}}}},{"id":2,"title":"Cost Estimation","properties":{"font":"Poppins","fontSize":{"lg":"22px","sm":"16px"},"fontLineHeight":{"lg":"30px","sm":"24px"},"backgroundColor":"","backgroundImageFocalPoint":{"x":0.460000000000000019984014443252817727625370025634765625,"y":0.320000000000000006661338147750939242541790008544921875},"logo":[],"typographyPreset":"md","questionsLabelFont":"Inherit","questionsLabelFontSize":{"lg":"28px","sm":"20px"},"questionsLabelLineHeight":{"lg":"32px","sm":"28px"},"questionsDescriptionFont":"Inherit","questionsDescriptionFontSize":{"lg":"18px","sm":"16px"},"questionsDescriptionLineHeight":{"lg":"20px","sm":"24px"},"questionsColor":"#ffffff","answersColor":"#ffffff","textInputAnswers":{"lg":"18px","sm":"24px"},"buttonsFontColor":"#fff","buttonsFontSize":{"lg":"20px","sm":"16px"},"buttonsPadding":{"top":{"lg":"9px","sm":"9px"},"bottom":{"lg":"9px","sm":"9px"},"left":{"lg":"23px","sm":"23px"},"right":{"lg":"20px","sm":"20px"}},"buttonsBgColor":"rgba(207, 46, 46, 1)","buttonsBorderRadius":25,"buttonsBorderWidth":0,"buttonsBorderColor":"#cf2e2e","errorsFontColor":"#ff0000","errorsBgColor":"rgba(255, 255, 255, 1)","progressBarFillColor":"#ff0000","progressBarBgColor":"#ffffff","formFooterBgColor":{"lg":"transparent","sm":"transparent"},"backgroundImage":"https:\/\/proactivezone.ae\/wp-content\/uploads\/2024\/05\/cost-calculator-design-.jpg","answersMargin":{"top":{"lg":"24px","sm":"24px"},"bottom":{"lg":"16px","sm":"16px"},"right":{"lg":"0px","sm":"0px"},"left":{"lg":"0px","sm":"0px"}}}},{"id":3,"title":"","properties":{"font":"Poppins","fontSize":{"lg":"20px","sm":"16px"},"fontLineHeight":{"lg":"28px","sm":"19px"},"backgroundColor":"linear-gradient(96deg,rgb(5,28,65) 0%,rgb(6,86,124) 63%,rgb(6,86,124) 100%)","backgroundImageFocalPoint":{"x":0.5,"y":0.5},"logo":[],"typographyPreset":"md","questionsLabelFont":"Poppins","questionsLabelFontSize":{"lg":"24px","sm":"20px"},"questionsLabelLineHeight":{"lg":"19px","sm":"22px"},"questionsDescriptionFont":"Poppins","questionsDescriptionFontSize":{"lg":"15px","sm":"15px"},"questionsDescriptionLineHeight":{"lg":"28px","sm":"24px"},"questionsColor":"#ffffff","answersColor":"#ffffff","textInputAnswers":{"lg":"20px","sm":"18px"},"buttonsFontColor":"#ffffff","buttonsFontSize":{"lg":"20px","sm":"16px"},"buttonsPadding":{"top":{"lg":"9px","sm":"9px"},"bottom":{"lg":"9px","sm":"9px"},"left":{"lg":"23px","sm":"23px"},"right":{"lg":"20px","sm":"20px"}},"buttonsBgColor":"rgba(255, 0, 0, 1)","buttonsBorderRadius":25,"buttonsBorderWidth":0,"buttonsBorderColor":"#f90000","errorsFontColor":"#af0404","errorsBgColor":"#f7e6e6","progressBarFillColor":"#ff0000","progressBarBgColor":"#ffffff","formFooterBgColor":{"lg":"transparent","sm":"transparent"}}}],"settings":{"disableProgressBar":false,"disableWheelSwiping":true,"disableNavigationArrows":true,"animationDirection":"vertical","showLettersOnAnswers":true,"showQuestionsNumbers":true,"saveAnswersInBrowser":true,"displayBranding":false},"customCSS":"","correctIncorrectQuiz":{"enabled":false,"questions":[],"showAnswersDuringQuiz":true}},"formId":7858,"customFonts":[],"_nonce":"785273ff2b"}
</script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/blocklib-group-block/renderer/index863f.js?ver=d42dd2b4adcc749df86a" id="quillforms-blocklib-group-block-renderer-script-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/blocklib-short-text-block/renderer/indexec6a.js?ver=88b4356670c5e19d3dca" id="quillforms-blocklib-short-text-block-renderer-script-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/build/blocklib-email-block/renderer/index21b7.js?ver=98d5ee01158b54d10d14" id="quillforms-blocklib-email-block-renderer-script-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms-phoneblock/build/renderer/index372b.js?ver=8eeaf9983341ecd2c896" id="quillforms-blocklib-phone-block-renderer-script-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/includes/render/render474a.js?ver=4.4.0" id="quillforms-react-renderer-script-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-includes/js/dist/dom-ready.min5346.js?ver=f77871ff7694fffea381" id="wp-dom-ready-js"></script>
<script data-no-optimize="1" data-two-no-delay="true" data-two-no-defer="true" data-pagespeed-no-defer="true" src="../../wp-content/plugins/quillforms/includes/render/iframe-resizer-content-window-min474a.js?ver=4.4.0" id="quillforms-iframe-resizer-content-window-script-js"></script>
            <script>Dropzone.autoDiscover = false; </script>   <style>
     .advancetawktocustomise {
         right: -31px !important;
              }

     .advancetawktocustomise-new-design {
         border-top-right-radius: 10px !important;
         border-top-left-radius: 10px !important;
         background: linear-gradient(45deg, #065c82, #052044) !important;
     }
 </style>
 <div class="advancetawktocustomise">
     <div class="advancetawktocustomise-new-design">
         <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
             <path d="M64 0C28.7 0 0 28.7 0 64V352c0 35.3 28.7 64 64 64h96v80c0 6.1 3.4 11.6 8.8 14.3s11.9 2.1 16.8-1.5L309.3 416H448c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64H64z" />
         </svg>
         <span>
             CHAT
         </span>
     </div>
 </div>	</body>

<!-- Mirrored from proactivezone.ae/quillforms/referral-earning/ by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 05 Jul 2025 12:41:04 GMT -->
</html>


<!-- Page supported by LiteSpeed Cache 6.5.4 on 2025-07-02 23:49:32 -->