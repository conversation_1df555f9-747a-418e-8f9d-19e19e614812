"use client";
import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";

interface InsightDetailScreenProps {
  id: string;
}

interface InsightDetail {
  id: number;
  title: string;
  content: string;
  excerpt: string;
  category: string;
  readTime: string;
  date: string;
  author: string;
}

const InsightDetailScreen: React.FC<InsightDetailScreenProps> = ({ id }) => {
  const { t, isRTL } = useLanguage();

  // Mock data - in a real app, this would come from an API or database
  const insights: InsightDetail[] = [
    {
      id: 1,
      title: t("insights.article1.title"),
      excerpt: t("insights.article1.description"),
      content: `
        <p>The UAE's tax landscape has undergone significant changes in recent years, with the introduction of Value Added Tax (VAT) in 2018 and Corporate Tax in 2023. Understanding these changes is crucial for businesses operating in the region.</p>
        
        <h2>VAT Implementation</h2>
        <p>The introduction of VAT at 5% marked a significant shift in the UAE's tax policy. Businesses with annual turnover exceeding AED 375,000 are required to register for VAT, while those with turnover between AED 187,500 and AED 375,000 can voluntarily register.</p>
        
        <h2>Corporate Tax Overview</h2>
        <p>The UAE Corporate Tax came into effect on June 1, 2023, applying to financial years starting on or after this date. The tax rate is set at 9% for taxable income exceeding AED 375,000, with a 0% rate for income up to this threshold.</p>
        
        <h2>Key Considerations</h2>
        <ul>
          <li>Proper record-keeping and documentation</li>
          <li>Regular compliance reviews</li>
          <li>Professional tax advisory services</li>
          <li>Understanding exemptions and reliefs</li>
        </ul>
        
        <p>Businesses should ensure they have robust systems in place to manage their tax obligations effectively. Professional guidance is recommended to navigate the complexities of the UAE tax system.</p>
      `,
      category: t("insights.categories.tax"),
      readTime: `5 ${t("common.minRead")}`,
      date: "2024-04-18",
      author: "PWM UAE Team",
    },
    {
      id: 2,
      title: t("insights.article2.title"),
      excerpt: t("insights.article2.description"),
      content: `
        <p>Trust structures have become increasingly important in wealth management and estate planning. Understanding the different types of trusts and their applications can help individuals and families protect and manage their assets effectively.</p>
        
        <h2>Types of Trust Structures</h2>
        <p>There are various types of trust structures available, each serving different purposes and offering unique benefits. The choice of trust structure depends on the specific needs and objectives of the settlor.</p>
        
        <h2>Benefits of Trust Structures</h2>
        <ul>
          <li>Asset protection and preservation</li>
          <li>Tax efficiency and planning</li>
          <li>Succession planning</li>
          <li>Privacy and confidentiality</li>
          <li>Professional management</li>
        </ul>
        
        <h2>Considerations</h2>
        <p>When establishing a trust structure, it's important to consider factors such as jurisdiction, regulatory requirements, tax implications, and ongoing compliance obligations.</p>
        
        <p>Professional advice from experienced trust and estate planning specialists is essential to ensure the structure meets your specific requirements and objectives.</p>
      `,
      category: t("insights.categories.trust"),
      readTime: `7 ${t("common.minRead")}`,
      date: "2024-03-28",
      author: "PWM UAE Team",
    },
    {
      id: 3,
      title: t("insights.article3.title"),
      excerpt: t("insights.article3.description"),
      content: `
        <p>The UAE continues to be one of the most attractive destinations for business setup and investment. With its strategic location, business-friendly policies, and world-class infrastructure, the UAE offers numerous opportunities for entrepreneurs and investors.</p>
        
        <h2>Key Advantages</h2>
        <ul>
          <li>Strategic location connecting East and West</li>
          <li>Tax-efficient environment</li>
          <li>World-class infrastructure</li>
          <li>Diverse economy and growing markets</li>
          <li>Stable political and economic environment</li>
        </ul>
        
        <h2>Business Setup Options</h2>
        <p>The UAE offers various business setup options including mainland companies, free zone entities, and offshore companies. Each option has its own advantages and is suitable for different types of business activities.</p>
        
        <h2>Investment Opportunities</h2>
        <p>From real estate and technology to renewable energy and healthcare, the UAE presents diverse investment opportunities across multiple sectors.</p>
        
        <p>Understanding the regulatory landscape and choosing the right structure is crucial for success in the UAE market.</p>
      `,
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2024-02-10",
      author: "PWM UAE Team",
    },
    {
      id: 4,
      title: t("International Tax Compliance"),
      excerpt: t(
        "Navigate the complex world of international tax regulations and ensure full compliance."
      ),
      content: `
        <p>The UAE continues to be one of the most attractive destinations for business setup and investment. With its strategic location, business-friendly policies, and world-class infrastructure, the UAE offers numerous opportunities for entrepreneurs and investors.</p>
        
        <h2>Key Advantages</h2>
        <ul>
          <li>Strategic location connecting East and West</li>
          <li>Tax-efficient environment</li>
          <li>World-class infrastructure</li>
          <li>Diverse economy and growing markets</li>
          <li>Stable political and economic environment</li>
        </ul>
        
        <h2>Business Setup Options</h2>
        <p>The UAE offers various business setup options including mainland companies, free zone entities, and offshore companies. Each option has its own advantages and is suitable for different types of business activities.</p>
        
        <h2>Investment Opportunities</h2>
        <p>From real estate and technology to renewable energy and healthcare, the UAE presents diverse investment opportunities across multiple sectors.</p>
        
        <p>Understanding the regulatory landscape and choosing the right structure is crucial for success in the UAE market.</p>
      `,
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2024-02-10",
      author: "PWM UAE Team",
    },
    {
      id: 5,
      title: t("Wealth Preservation Strategies"),
      excerpt: t(
        "Learn effective strategies to preserve and grow your wealth across different jurisdictions."
      ),
      content: `
        <p>The UAE continues to be one of the most attractive destinations for business setup and investment. With its strategic location, business-friendly policies, and world-class infrastructure, the UAE offers numerous opportunities for entrepreneurs and investors.</p>
        
        <h2>Key Advantages</h2>
        <ul>
          <li>Strategic location connecting East and West</li>
          <li>Tax-efficient environment</li>
          <li>World-class infrastructure</li>
          <li>Diverse economy and growing markets</li>
          <li>Stable political and economic environment</li>
        </ul>
        
        <h2>Business Setup Options</h2>
        <p>The UAE offers various business setup options including mainland companies, free zone entities, and offshore companies. Each option has its own advantages and is suitable for different types of business activities.</p>
        
        <h2>Investment Opportunities</h2>
        <p>From real estate and technology to renewable energy and healthcare, the UAE presents diverse investment opportunities across multiple sectors.</p>
        
        <p>Understanding the regulatory landscape and choosing the right structure is crucial for success in the UAE market.</p>
      `,
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2024-02-10",
      author: "PWM UAE Team",
    },
    {
      id: 6,
      title: t("Dubai Free Zone Benefits"),
      excerpt: t(
        "Explore the advantages of setting up your business in Dubai's various free zones."
      ),
      content: `
        <p>The UAE continues to be one of the most attractive destinations for business setup and investment. With its strategic location, business-friendly policies, and world-class infrastructure, the UAE offers numerous opportunities for entrepreneurs and investors.</p>
        
        <h2>Key Advantages</h2>
        <ul>
          <li>Strategic location connecting East and West</li>
          <li>Tax-efficient environment</li>
          <li>World-class infrastructure</li>
          <li>Diverse economy and growing markets</li>
          <li>Stable political and economic environment</li>
        </ul>
        
        <h2>Business Setup Options</h2>
        <p>The UAE offers various business setup options including mainland companies, free zone entities, and offshore companies. Each option has its own advantages and is suitable for different types of business activities.</p>
        
        <h2>Investment Opportunities</h2>
        <p>From real estate and technology to renewable energy and healthcare, the UAE presents diverse investment opportunities across multiple sectors.</p>
        
        <p>Understanding the regulatory landscape and choosing the right structure is crucial for success in the UAE market.</p>
      `,
      category: t("insights.categories.business"),
      readTime: `6 ${t("common.minRead")}`,
      date: "2024-02-10",
      author: "PWM UAE Team",
    },
  ];

  const insight = insights.find((item) => item.id === parseInt(id));

  if (!insight) {
    return (
      <div className={`min-h-screen bg-white ${isRTL ? "rtl" : "ltr"}`}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {t("insights.notFound.title") || "Insight Not Found"}
            </h1>
            <p className="text-gray-600 mb-8">
              {t("insights.notFound.description") ||
                "The insight you're looking for doesn't exist."}
            </p>
            <Button
              as={Link}
              href="/insights"
              className="bg-primary text-white hover:bg-primary/90"
            >
              {t("insights.backToInsights") || "Back to Insights"}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-white ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#e9f5ec] to-[#f0f9f3] py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Button
                as={Link}
                href="/insights"
                variant="flat"
                className="text-primary hover:bg-primary/10 mb-4"
              >
                ← {t("insights.backToInsights") || "Back to Insights"}
              </Button>
            </div>

            <div className="flex items-center gap-2 mb-4">
              <span className="text-sm bg-primary/10 text-primary px-3 py-1 rounded-full font-medium">
                {insight.category}
              </span>
              <span className="text-sm text-gray-600">{insight.readTime}</span>
              <span className="text-sm text-gray-600">•</span>
              <span className="text-sm text-gray-600">
                {new Date(insight.date).toLocaleDateString()}
              </span>
            </div>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6 text-primary">
              {insight.title}
            </h1>

            <p className="text-xl text-gray-700 mb-8">{insight.excerpt}</p>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>By {insight.author}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div
              className="prose prose-lg max-w-none prose-headings:text-primary prose-a:text-primary"
              dangerouslySetInnerHTML={{ __html: insight.content }}
            />

            {/* Share Section */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <h3 className="text-lg font-semibold mb-4 text-gray-900">
                {t("insights.share") || "Share this insight"}
              </h3>
              <div className="flex gap-4">
                <button className="text-primary hover:text-primary/80 font-medium">
                  LinkedIn
                </button>
                <button className="text-primary hover:text-primary/80 font-medium">
                  Twitter
                </button>
                <button className="text-primary hover:text-primary/80 font-medium">
                  Email
                </button>
              </div>
            </div>

            {/* Related Insights */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold mb-8 text-gray-900">
                {t("insights.related") || "Related Insights"}
              </h3>
              <div className="grid md:grid-cols-2 gap-8">
                {insights
                  .filter(
                    (item) =>
                      item.id !== insight.id &&
                      item.category === insight.category
                  )
                  .slice(0, 2)
                  .map((relatedInsight) => (
                    <article
                      key={relatedInsight.id}
                      className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden hover:border-gray-300 hover:shadow-sm transition-all"
                    >
                      <div className="h-32 bg-gradient-to-br from-primary/10 to-primary/20"></div>
                      <div className="p-6">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                            {relatedInsight.category}
                          </span>
                          <span className="text-xs text-gray-600">
                            {relatedInsight.readTime}
                          </span>
                        </div>
                        <h4 className="text-lg font-semibold mb-2 hover:text-primary cursor-pointer text-gray-900">
                          <Link href={`/insights/${relatedInsight.id}`}>
                            {relatedInsight.title}
                          </Link>
                        </h4>
                        <p className="text-gray-700 text-sm line-clamp-2">
                          {relatedInsight.excerpt}
                        </p>
                      </div>
                    </article>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightDetailScreen;
